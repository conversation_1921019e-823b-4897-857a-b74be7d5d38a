---
import Footer from "@components/Footer.astro";
import BackLink from "@components/BackLink.astro";

const { pageTitle, showBackLink = false, backLinkTitle, backLinkUrl } = Astro.props;
---

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="generator" content={Astro.generator} />
    <title>{pageTitle}</title>
  </head>

  <body>
    <div class="grid-container">
      <aside class="left-column">
        {showBackLink && <BackLink title={backLinkTitle} url={backLinkUrl} />}
      </aside>

      <main class="main-content">
        <slot />
      </main>

      <aside class="right-column">
        <!-- Right column remains empty by default -->
      </aside>
    </div>

    <Footer />
  </body>
</html>
