---
const { title, url } = Astro.props;
---

<nav class="back-link">
  <a href={url}>
    <div class="title">
      <svg
        id="arrow-left"
        viewBox="0 0 24 24"
        width="20"
        height='20"'
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="m19 15.477-.02-9.672a.802.802 0 0 0-.218-.577c-.145-.152-.34-.228-.587-.228H8.499a.751.751 0 0 0-.777.76c0 .199.076.371.227.517.15.145.326.218.525.218h3.733l4.52-.129-1.728 1.54-9.767 9.783a.692.692 0 0 0-.232.518c0 .205.078.387.235.545a.74.74 0 0 0 .542.237.73.73 0 0 0 .527-.224l9.775-9.78 1.544-1.727-.143 4.188v4.065c0 .199.075.376.225.531.15.155.327.232.531.232.202 0 .38-.076.534-.228a.768.768 0 0 0 .23-.569Z"
          fill="#000"></path>
      </svg>
      <span><em>{title}</em></span>
    </div>
  </a>
</nav>

<style>
  a {
    font-family: var(--font-tertiary);
    font-size: 1.063rem;

    text-decoration: none;
  }

  @media only screen and (min-width: 993px) {
    nav {
      position: fixed;
      top: 0;
      left: 0;

      padding: var(--content-padding);
      padding-left: clamp(24px, 20vw, 512px);
    }
  }

  .back-link {
    margin-bottom: 42px;
  }

  .title {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 8px;
  }

  .title > span {
    font-weight: 500;
  }

  svg path {
    fill: var(--color-primary);
  }

  #arrow-left {
    transform: scaleX(-1);
  }
</style>
