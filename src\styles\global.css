@import url("https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Newsreader:ital,opsz,wght@0,6..72,200..800;1,6..72,200..800&display=swap");

@font-face {
  font-family: "Overused Grotesk";
  src: url("https://raw.githubusercontent.com/RandomMaerks/Overused-Grotesk/master/fonts/variable/OverusedGroteskRoman-VF.ttf");
  font-weight: 300 900;
}

:root {
  color-scheme: light dark;

  --color-primary: light-dark(#3a3a3a, #e5e5e5);
  --color-secondary: light-dark(#6f6f6f, #a0a0a0);
  --color-tertiary: light-dark(#707070, #8f8f8f);
  --color-bg: light-dark(#fff, #1a1a1a);
  --color-underline: light-dark(#a0a0a0, #707070);

  --font-primary: "Overused Grotesk";
  --font-secondary: "Inter";
  --font-tertiary: "Newsreader";

  --page-width: 1072px;
  --content-width: 640px;
  --content-padding: clamp(64px, 10vw, 128px) 0;
}

*, *::before, *::after {
  margin: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
}

body {
  font-family:
    var(--font-primary),
    sans-serif;

  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  padding: 0px 24px;

  line-height: 1.6;

  background-color: var(--color-bg);

  -webkit-font-smoothing: antialiased;
}

.grid-container {
  display: grid;
  grid-template-columns: 192px var(--content-width) 192px;
  width: 100%;
  max-width: calc(192px + var(--content-width) + 192px);
  flex: 1 0 auto;
}

.left-column {
  grid-column: 1;
  padding: var(--content-padding);
  padding-right: 0;
}

.main-content {
  grid-column: 2;
  padding: var(--content-padding);
  width: 100%;
}

.right-column {
  grid-column: 3;
  padding: var(--content-padding);
  padding-left: 0;
}

/* Mobile responsive: collapse to single column */
@media (max-width: 768px) {
  .grid-container {
    display: block;
  }

  .left-column,
  .main-content,
  .right-column {
    grid-column: unset;
    padding: 0;
  }

  .main-content {
    padding: var(--content-padding);
  }

  .left-column {
    padding-bottom: 0;
  }
}

section {
  margin-bottom: 64px;
}

h1,
h2,
h3 {
  font-family:
    var(--font-secondary),
    sans-serif;
  font-size: 1rem;
  font-weight: 500;
  line-height: 1.2;

  margin: 0 0 28px;

  color: var(--color-primary);
}

a {
  text-underline-offset: 2.5px;
  text-decoration: underline;
  text-decoration-thickness: 1px;
  text-decoration-color: var(--color-underline);

  color: var(--color-primary);
}

a:hover {
  text-decoration-color: var(--color-primary);
}

p {
  text-wrap: pretty;
  color: var(--color-primary);

  margin: 0 0 28px;
}
